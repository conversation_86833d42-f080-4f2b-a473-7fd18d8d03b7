#!/usr/bin/env python3
"""
测试 aiohttp 的代理连接
"""
import asyncio
import aiohttp
import sys

async def test_aiohttp_proxy():
    """测试 aiohttp 通过代理连接 OKX"""
    
    print("🔍 测试 aiohttp 代理连接...")
    
    # 代理配置
    proxy = "http://127.0.0.1:8118"
    
    # 测试 URL
    test_urls = [
        "https://www.okx.com/api/v5/public/time",
        "https://www.okx.com/api/v5/public/instruments?instType=SPOT&limit=5"
    ]
    
    # 创建连接器
    connector = aiohttp.TCPConnector(
        limit=100,
        limit_per_host=30,
        ttl_dns_cache=300,
        use_dns_cache=True,
    )
    
    # 创建会话
    timeout = aiohttp.ClientTimeout(total=30)
    
    async with aiohttp.ClientSession(
        connector=connector,
        timeout=timeout
    ) as session:
        
        for i, url in enumerate(test_urls, 1):
            print(f"\n📡 测试 {i}: {url}")
            
            try:
                # 尝试通过代理请求
                async with session.get(
                    url,
                    proxy=proxy,
                    headers={
                        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
                    }
                ) as response:
                    print(f"✅ 状态码: {response.status}")
                    if response.status == 200:
                        data = await response.json()
                        print(f"✅ 响应数据: {str(data)[:100]}...")
                    else:
                        print(f"❌ HTTP 错误: {response.status}")
                        
            except Exception as e:
                print(f"❌ 连接失败: {e}")
                print(f"错误类型: {type(e).__name__}")
                
                # 尝试不使用代理
                print("🔄 尝试不使用代理...")
                try:
                    async with session.get(url) as response:
                        print(f"✅ 直连成功，状态码: {response.status}")
                except Exception as e2:
                    print(f"❌ 直连也失败: {e2}")

if __name__ == "__main__":
    asyncio.run(test_aiohttp_proxy())
