#!/bin/bash

# 合约数据下载脚本
echo "🚀 开始下载合约历史数据（2年）"
echo "================================"

# 设置代理
echo "🌐 设置网络代理..."
export http_proxy="http://127.0.0.1:8118"
export https_proxy="http://127.0.0.1:8118"
export HTTP_PROXY="http://127.0.0.1:8118"
export HTTPS_PROXY="http://127.0.0.1:8118"

echo "✅ 代理设置完成"
echo "   HTTP_PROXY: $HTTP_PROXY"
echo "   HTTPS_PROXY: $HTTPS_PROXY"

# 激活虚拟环境
echo ""
echo "🔧 激活虚拟环境..."
source .venv/bin/activate

# 测试网络连接
echo ""
echo "🔍 测试网络连接..."
if curl -s --connect-timeout 5 --proxy $http_proxy https://api.binance.com/api/v3/ping > /dev/null; then
    echo "✅ 网络连接正常"
else
    echo "❌ 网络连接失败，请检查代理设置"
    echo "💡 请确保您的VPN和代理正在运行"
    exit 1
fi

# 开始下载数据
echo ""
echo "📥 开始下载合约数据..."
echo "⏳ 这可能需要5-10分钟，请耐心等待..."
echo ""

# 分批下载，避免超时
echo "📊 下载 BTC/USDT:USDT 数据..."
python -m freqtrade download-data --config user_data/config_futures.json --pairs BTC/USDT:USDT --timeframes 1m --days 730

if [ $? -eq 0 ]; then
    echo "✅ BTC/USDT:USDT 下载完成"
else
    echo "❌ BTC/USDT:USDT 下载失败"
fi

echo ""
echo "📊 下载 ETH/USDT:USDT 数据..."
python -m freqtrade download-data --config user_data/config_futures.json --pairs ETH/USDT:USDT --timeframes 1m --days 730

if [ $? -eq 0 ]; then
    echo "✅ ETH/USDT:USDT 下载完成"
else
    echo "❌ ETH/USDT:USDT 下载失败"
fi

echo ""
echo "📊 下载 BNB/USDT:USDT 数据..."
python -m freqtrade download-data --config user_data/config_futures.json --pairs BNB/USDT:USDT --timeframes 1m --days 730

if [ $? -eq 0 ]; then
    echo "✅ BNB/USDT:USDT 下载完成"
else
    echo "❌ BNB/USDT:USDT 下载失败"
fi

echo ""
echo "📊 下载 SOL/USDT:USDT 数据..."
python -m freqtrade download-data --config user_data/config_futures.json --pairs SOL/USDT:USDT --timeframes 1m --days 730

if [ $? -eq 0 ]; then
    echo "✅ SOL/USDT:USDT 下载完成"
else
    echo "❌ SOL/USDT:USDT 下载失败"
fi

echo ""
echo "🎉 数据下载完成！"
echo ""
echo "📊 检查下载的数据..."
ls -la user_data/data/binance/

echo ""
echo "✅ 现在可以运行合约策略回测了！"
echo "💡 使用命令：./🚀_一键启动.sh 然后选择 5"
