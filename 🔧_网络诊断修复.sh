#!/bin/bash

# 网络连接诊断和修复脚本
echo "🔧 Freqtrade 网络连接诊断和修复"
echo "=================================="

# 1. 检查当前代理设置
echo ""
echo "🔍 第一步：检查当前代理设置"
echo "HTTP_PROXY: $HTTP_PROXY"
echo "HTTPS_PROXY: $HTTPS_PROXY"
echo "http_proxy: $http_proxy"
echo "https_proxy: $https_proxy"

# 2. 强制设置代理环境变量
echo ""
echo "🌐 第二步：设置代理环境变量"
export HTTP_PROXY="http://127.0.0.1:8118"
export HTTPS_PROXY="http://127.0.0.1:8118"
export http_proxy="http://127.0.0.1:8118"
export https_proxy="http://127.0.0.1:8118"
export ALL_PROXY="http://127.0.0.1:8118"
export all_proxy="http://127.0.0.1:8118"

echo "✅ 代理设置完成："
echo "   HTTP_PROXY: $HTTP_PROXY"
echo "   HTTPS_PROXY: $HTTPS_PROXY"

# 3. 检查代理服务是否运行
echo ""
echo "🔍 第三步：检查代理服务"
if nc -z 127.0.0.1 8118; then
    echo "✅ 代理服务 127.0.0.1:8118 正在运行"
else
    echo "❌ 代理服务 127.0.0.1:8118 未运行"
    echo "💡 请确保您的VPN和代理软件正在运行"
    echo "💡 常见代理软件端口："
    echo "   - ClashX: 7890"
    echo "   - V2rayU: 1087"
    echo "   - Shadowsocks: 1086"
    echo ""
    echo "🔧 尝试其他常见端口..."
    
    for port in 7890 1087 1086 8080 1080; do
        if nc -z 127.0.0.1 $port; then
            echo "✅ 发现代理服务在端口 $port"
            export HTTP_PROXY="http://127.0.0.1:$port"
            export HTTPS_PROXY="http://127.0.0.1:$port"
            export http_proxy="http://127.0.0.1:$port"
            export https_proxy="http://127.0.0.1:$port"
            echo "🔄 已切换到端口 $port"
            break
        fi
    done
fi

# 4. 测试基本网络连接
echo ""
echo "🔍 第四步：测试网络连接"
echo "测试 Google..."
if curl -s --connect-timeout 5 --proxy $http_proxy https://www.google.com > /dev/null; then
    echo "✅ Google 连接成功"
else
    echo "❌ Google 连接失败"
fi

echo "测试 Binance API..."
if curl -s --connect-timeout 10 --proxy $http_proxy https://api.binance.com/api/v3/ping; then
    echo "✅ Binance API 连接成功"
else
    echo "❌ Binance API 连接失败"
fi

# 5. 测试 Python requests
echo ""
echo "🔍 第五步：测试 Python 网络连接"
source .venv/bin/activate

python3 << EOF
import requests
import os

# 设置代理
proxies = {
    'http': os.environ.get('http_proxy'),
    'https': os.environ.get('https_proxy')
}

print(f"使用代理: {proxies}")

try:
    # 测试 Binance API
    response = requests.get('https://api.binance.com/api/v3/ping', 
                          proxies=proxies, 
                          timeout=10)
    print(f"✅ Python requests 测试成功: {response.status_code}")
    print(f"响应: {response.json()}")
except Exception as e:
    print(f"❌ Python requests 测试失败: {e}")

# 测试 CCXT
try:
    import ccxt
    exchange = ccxt.binance({
        'proxies': proxies,
        'timeout': 30000,
        'enableRateLimit': True
    })
    
    ticker = exchange.fetch_ticker('BTC/USDT')
    print(f"✅ CCXT 测试成功: BTC/USDT = \${ticker['last']}")
except Exception as e:
    print(f"❌ CCXT 测试失败: {e}")
EOF

# 6. 修复 Freqtrade 配置
echo ""
echo "🔧 第六步：修复 Freqtrade 配置"

# 更新配置文件中的代理设置
python3 << EOF
import json

# 更新现货配置
try:
    with open('user_data/config.json', 'r') as f:
        config = json.load(f)
    
    config['exchange']['ccxt_config']['proxies'] = {
        'http': '$http_proxy',
        'https': '$https_proxy'
    }
    config['exchange']['ccxt_async_config']['proxies'] = {
        'http': '$http_proxy',
        'https': '$https_proxy'
    }
    
    with open('user_data/config.json', 'w') as f:
        json.dump(config, f, indent=4)
    
    print("✅ 现货配置文件已更新")
except Exception as e:
    print(f"❌ 现货配置更新失败: {e}")

# 更新合约配置
try:
    with open('user_data/config_futures.json', 'r') as f:
        config = json.load(f)
    
    config['exchange']['ccxt_config']['proxies'] = {
        'http': '$http_proxy',
        'https': '$https_proxy'
    }
    config['exchange']['ccxt_async_config']['proxies'] = {
        'http': '$http_proxy',
        'https': '$https_proxy'
    }
    
    with open('user_data/config_futures.json', 'w') as f:
        json.dump(config, f, indent=4)
    
    print("✅ 合约配置文件已更新")
except Exception as e:
    print(f"❌ 合约配置更新失败: {e}")
EOF

# 7. 最终测试
echo ""
echo "🚀 第七步：最终测试 Freqtrade 连接"
echo "测试配置验证..."

if python -m freqtrade show-config --config user_data/config_futures.json > /dev/null 2>&1; then
    echo "✅ Freqtrade 配置验证成功"
else
    echo "❌ Freqtrade 配置验证失败"
fi

echo ""
echo "🎉 网络诊断完成！"
echo ""
echo "📋 诊断结果总结："
echo "   代理地址: $http_proxy"
echo "   配置文件: 已更新"
echo ""
echo "💡 下一步操作："
echo "   1. 如果所有测试都成功，可以开始下载数据"
echo "   2. 如果仍有问题，请检查VPN/代理软件设置"
echo "   3. 尝试重启代理软件或更换代理端口"
echo ""
echo "🚀 现在可以运行："
echo "   ./🚀_一键启动.sh"
