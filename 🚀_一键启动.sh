#!/bin/bash

# Freqtrade 小白一键启动脚本
# 使用方法：双击运行或在终端执行 ./🚀_一键启动.sh

clear
echo "🚀 欢迎使用 Freqtrade 自动交易机器人"
echo "=================================="
echo ""

# 检查是否在正确目录
if [ ! -f "user_data/config.json" ]; then
    echo "❌ 错误：请确保在 freqtrade 目录下运行此脚本"
    echo "正确路径应该是：/Users/<USER>/freqtrade"
    exit 1
fi

# 设置代理（重要！）
echo "🌐 设置网络代理..."
export http_proxy="http://127.0.0.1:8118"
export https_proxy="http://127.0.0.1:8118"
echo "✅ 代理设置完成"
echo ""

# 激活虚拟环境
echo "🔧 激活虚拟环境..."
source .venv/bin/activate
if [ $? -eq 0 ]; then
    echo "✅ 虚拟环境激活成功"
else
    echo "❌ 虚拟环境激活失败"
    exit 1
fi
echo ""

# 显示菜单
echo "请选择运行模式："
echo ""
echo "🔰 现货交易（新手推荐）："
echo "  1) Web界面模式 - 图形化界面，最简单易用"
echo "  2) 干跑模式 - 模拟交易，安全学习"
echo ""
echo "⚡ 合约交易（超短线）："
echo "  3) 合约Web界面 - 合约策略图形界面"
echo "  4) 合约干跑模式 - 合约模拟交易"
echo "  5) 合约回测 - 测试合约策略（2年数据）"
echo ""
echo "🔬 数据和分析："
echo "  6) 下载现货数据 - 获取现货历史数据"
echo "  7) 下载合约数据 - 获取合约历史数据（2年）"
echo "  8) 现货回测 - 测试现货策略"
echo ""
echo "⚠️  实盘交易："
echo "  9) 现货实盘 - 真实现货交易"
echo ""
echo "🛠️  其他："
echo "  6) 查看配置 - 检查当前设置"
echo "  7) 查看帮助 - 打开详细说明"
echo "  0) 退出"
echo ""

read -p "请输入选择 (0-9): " choice

case $choice in
    1)
        echo ""
        echo "🌐 启动现货Web界面模式..."
        echo "⏳ 请稍等，正在启动服务器..."
        echo ""
        echo "📝 启动后请："
        echo "   1. 打开浏览器"
        echo "   2. 访问：http://127.0.0.1:8080"
        echo "   3. 用户名：freqtrader"
        echo "   4. 密码：SuperSecurePassword"
        echo ""
        echo "🛑 停止方法：按 Ctrl+C"
        echo ""
        python -m freqtrade webserver --config user_data/config.json
        ;;
    2)
        echo ""
        echo "🔒 启动现货干跑模式（模拟交易）..."
        echo "💡 这是安全的模拟模式，不会花真钱"
        echo "🛑 停止方法：按 Ctrl+C"
        echo ""
        python -m freqtrade trade --config user_data/config.json --dry-run
        ;;
    3)
        echo ""
        echo "⚡ 启动合约Web界面模式..."
        echo "🎯 超短线合约策略 - 1分钟K线，最高10倍杠杆"
        echo ""
        echo "📝 启动后请："
        echo "   1. 打开浏览器"
        echo "   2. 访问：http://127.0.0.1:8080"
        echo "   3. 用户名：futures_trader"
        echo "   4. 密码：FuturesScalping2025!"
        echo ""
        echo "🛑 停止方法：按 Ctrl+C"
        echo ""
        python -m freqtrade webserver --config user_data/config_futures.json
        ;;
    4)
        echo ""
        echo "⚡ 启动合约干跑模式..."
        echo "🎯 超短线合约策略模拟交易"
        echo "💡 这是安全的模拟模式，不会花真钱"
        echo "🛑 停止方法：按 Ctrl+C"
        echo ""
        python -m freqtrade trade --config user_data/config_futures.json --dry-run
        ;;
    5)
        echo ""
        echo "📊 启动合约策略回测（2年数据）..."
        echo "⚠️  请确保已下载合约历史数据（选项7）"
        echo ""
        read -p "确认继续？(y/n): " confirm
        if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
            echo "🚀 开始回测合约策略..."
            python -m freqtrade backtesting --config user_data/config_futures.json --strategy FuturesScalpingStrategy --timerange=20230101-20250101
        else
            echo "已取消"
        fi
        ;;
    6)
        echo ""
        echo "📥 下载现货历史数据..."
        echo "⏳ 正在下载现货数据（30天，5分钟）..."
        echo ""
        python -m freqtrade download-data --config user_data/config.json --pairs BTC/USDT ETH/USDT BNB/USDT --timeframes 5m --days 30
        echo ""
        echo "✅ 现货数据下载完成！"
        ;;
    7)
        echo ""
        echo "📥 下载合约历史数据（2年）..."
        echo "⏳ 正在下载合约数据，这可能需要几分钟..."
        echo ""
        python -m freqtrade download-data --config user_data/config_futures.json --pairs BTC/USDT:USDT ETH/USDT:USDT BNB/USDT:USDT SOL/USDT:USDT --timeframes 1m --days 730
        echo ""
        echo "✅ 合约数据下载完成！现在可以进行2年回测了"
        ;;
    8)
        echo ""
        echo "📊 启动现货策略回测..."
        echo "⚠️  请确保已下载现货历史数据（选项6）"
        echo ""
        read -p "确认继续？(y/n): " confirm
        if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
            python -m freqtrade backtesting --config user_data/config.json --strategy SampleStrategy
        else
            echo "已取消"
        fi
        ;;
    9)
        echo ""
        echo "⚠️  警告：现货实盘模式会使用真实资金！"
        echo "📋 前提条件："
        echo "   - Binance API 必须开启交易权限"
        echo "   - 账户有足够资金"
        echo "   - 充分理解交易风险"
        echo ""
        read -p "您确定要启动现货实盘模式吗？(yes/no): " confirm
        if [ "$confirm" = "yes" ]; then
            echo "🚀 启动现货实盘模式..."
            python -m freqtrade trade --config user_data/config.json
        else
            echo "已取消，建议先使用干跑模式练习"
        fi
        ;;
    0)
        echo ""
        echo "👋 再见！感谢使用 Freqtrade"
        exit 0
        ;;
    *)
        echo ""
        echo "❌ 无效选择，请输入 0-9 之间的数字"
        ;;
esac

echo ""
echo "🔄 程序已结束，按任意键退出..."
read -n 1
