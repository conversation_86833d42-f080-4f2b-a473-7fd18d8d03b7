#!/bin/bash

# Freqtrade 高频交易启动脚本
# 优化系统性能和网络连接

echo "🚀 启动 Freqtrade 高频交易模式..."

# 激活虚拟环境
source .venv/bin/activate

# 设置环境变量优化性能
export PYTHONUNBUFFERED=1
export UVLOOP_ENABLED=1
export ORJSON_ENABLED=1

# 设置网络优化
export CCXT_ENABLE_RATE_LIMIT=true
export CCXT_TIMEOUT=10000

# 检查网络连接
echo "📡 检查网络连接..."
ping -c 1 api.binance.com > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ 网络连接正常"
else
    echo "❌ 网络连接异常，请检查网络"
    exit 1
fi

# 选择运行模式
echo "请选择运行模式："
echo "1) 干跑模式 (安全测试)"
echo "2) 实盘模式 (真实交易)"
echo "3) Web服务器模式"
echo "4) 回测模式"

read -p "请输入选择 (1-4): " choice

case $choice in
    1)
        echo "🔒 启动干跑模式..."
        python -m freqtrade trade --config user_data/config_fast.json --dry-run
        ;;
    2)
        echo "💰 启动实盘模式..."
        echo "⚠️  警告：这将进行真实交易！"
        read -p "确认继续？(yes/no): " confirm
        if [ "$confirm" = "yes" ]; then
            python -m freqtrade trade --config user_data/config_fast.json
        else
            echo "已取消"
        fi
        ;;
    3)
        echo "🌐 启动Web服务器..."
        python -m freqtrade webserver --config user_data/config_fast.json
        ;;
    4)
        echo "📊 启动回测模式..."
        python -m freqtrade backtesting --config user_data/config_fast.json --strategy SampleStrategy
        ;;
    *)
        echo "无效选择"
        exit 1
        ;;
esac
