#!/bin/bash

# Freqtrade 网络连接配置脚本
# 用于解决 Binance API 连接问题

echo "🌐 Freqtrade 网络连接配置"
echo "=========================="

# 检测当前网络环境
echo "正在检测网络环境..."

# 测试直连
if curl -s --connect-timeout 5 https://api.binance.com/api/v3/ping > /dev/null 2>&1; then
    echo "✅ 直连 Binance API 成功"
    PROXY_NEEDED=false
else
    echo "❌ 直连 Binance API 失败"
    PROXY_NEEDED=true
fi

if [ "$PROXY_NEEDED" = true ]; then
    echo ""
    echo "🔧 需要配置网络代理"
    echo "请选择解决方案："
    echo "1) 配置 HTTP 代理"
    echo "2) 使用备用 DNS"
    echo "3) 修改 hosts 文件"
    echo "4) 跳过，使用离线模式"
    
    read -p "请选择 (1-4): " choice
    
    case $choice in
        1)
            echo "请输入代理信息："
            read -p "HTTP 代理地址 (例: http://127.0.0.1:7890): " http_proxy
            read -p "HTTPS 代理地址 (例: http://127.0.0.1:7890): " https_proxy
            
            # 更新配置文件
            python3 << EOF
import json

# 更新主配置
with open('user_data/config.json', 'r') as f:
    config = json.load(f)

config['exchange']['ccxt_config']['proxies'] = {
    'http': '$http_proxy',
    'https': '$https_proxy'
}
config['exchange']['ccxt_async_config']['proxies'] = {
    'http': '$http_proxy', 
    'https': '$https_proxy'
}

with open('user_data/config.json', 'w') as f:
    json.dump(config, f, indent=4)

# 更新快速配置
with open('user_data/config_fast.json', 'r') as f:
    config = json.load(f)

config['exchange']['ccxt_config']['proxies'] = {
    'http': '$http_proxy',
    'https': '$https_proxy'
}
config['exchange']['ccxt_async_config']['proxies'] = {
    'http': '$http_proxy',
    'https': '$https_proxy'
}

with open('user_data/config_fast.json', 'w') as f:
    json.dump(config, f, indent=4)

print("✅ 代理配置已更新")
EOF
            ;;
        2)
            echo "配置备用 DNS..."
            # 使用 Cloudflare DNS
            sudo networksetup -setdnsservers Wi-Fi ******* *******
            echo "✅ DNS 已更新为 Cloudflare (*******)"
            ;;
        3)
            echo "修改 hosts 文件..."
            echo "# Binance API hosts" | sudo tee -a /etc/hosts
            echo "************ api.binance.com" | sudo tee -a /etc/hosts
            echo "************ dapi.binance.com" | sudo tee -a /etc/hosts
            echo "✅ hosts 文件已更新"
            ;;
        4)
            echo "⚠️  将使用离线模式，仅支持回测"
            ;;
    esac
fi

echo ""
echo "🧪 测试连接..."
if curl -s --connect-timeout 10 https://api.binance.com/api/v3/ping > /dev/null 2>&1; then
    echo "✅ Binance API 连接成功！"
else
    echo "❌ 连接仍然失败"
    echo "💡 建议："
    echo "   1. 检查代理设置是否正确"
    echo "   2. 尝试使用 VPN"
    echo "   3. 联系网络管理员"
fi

echo ""
echo "🚀 配置完成！现在可以启动 Freqtrade"
