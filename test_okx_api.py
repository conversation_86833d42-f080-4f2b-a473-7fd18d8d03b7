#!/usr/bin/env python3
"""
测试 OKX API 连接
"""
import ccxt
import sys

def test_okx_api():
    """测试 OKX API 连接"""
    
    # API 配置
    api_key = "2cfd6199-0cfc-4e95-83b7-ab7470323382"
    secret = "E69EA23CDF0D75B63A007266F676B3F0"
    passphrase = "Zhuyao@1988"
    
    print("🔍 测试 OKX API 连接...")
    print(f"API Key: {api_key[:8]}...")
    print(f"Secret: {secret[:8]}...")
    print(f"Passphrase: {'*' * len(passphrase)}")
    print("-" * 50)
    
    try:
        # 创建 OKX 交易所实例
        exchange = ccxt.okx({
            'apiKey': api_key,
            'secret': secret,
            'password': passphrase,
            'sandbox': False,  # 使用实盘环境
            'enableRateLimit': True,
            'timeout': 30000,  # 30秒超时
            'options': {
                'defaultType': 'swap'  # 期货交易
            },
            'proxies': {
                'http': 'http://127.0.0.1:8118',
                'https': 'http://127.0.0.1:8118'
            },
            'verbose': True  # 启用详细日志
        })
        
        print("✅ 交易所实例创建成功")
        
        # 测试1: 获取服务器时间
        print("\n📅 测试1: 获取服务器时间...")
        try:
            server_time = exchange.fetch_time()
            print(f"✅ 服务器时间: {server_time}")
        except Exception as e:
            print(f"❌ 获取服务器时间失败: {e}")
            return False
        
        # 测试2: 获取账户信息
        print("\n💰 测试2: 获取账户信息...")
        try:
            balance = exchange.fetch_balance()
            print(f"✅ 账户信息获取成功")
            print(f"总资产: {balance.get('total', {})}")
        except Exception as e:
            print(f"❌ 获取账户信息失败: {e}")
            print("这可能是正常的，如果是只读权限的话")
        
        # 测试3: 获取市场数据
        print("\n📊 测试3: 获取 BTC/USDT 市场数据...")
        try:
            ticker = exchange.fetch_ticker('BTC/USDT:USDT')
            print(f"✅ BTC/USDT 价格: {ticker['last']}")
            print(f"24h 涨跌幅: {ticker['percentage']:.2f}%")
        except Exception as e:
            print(f"❌ 获取市场数据失败: {e}")
            return False
        
        # 测试4: 获取K线数据
        print("\n📈 测试4: 获取 K线数据...")
        try:
            ohlcv = exchange.fetch_ohlcv('BTC/USDT:USDT', '1m', limit=5)
            print(f"✅ 获取到 {len(ohlcv)} 条K线数据")
            if ohlcv:
                latest = ohlcv[-1]
                print(f"最新K线: 开盘:{latest[1]}, 最高:{latest[2]}, 最低:{latest[3]}, 收盘:{latest[4]}")
        except Exception as e:
            print(f"❌ 获取K线数据失败: {e}")
            return False
        
        print("\n🎉 所有测试通过！OKX API 工作正常")
        return True
        
    except Exception as e:
        print(f"❌ 创建交易所实例失败: {e}")
        return False

if __name__ == "__main__":
    success = test_okx_api()
    sys.exit(0 if success else 1)
