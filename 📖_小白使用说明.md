# 🚀 Freqtrade 小白完全使用指南

## 📋 目录
1. [基础知识](#基础知识)
2. [启动前准备](#启动前准备)
3. [四种使用模式](#四种使用模式)
4. [Web界面使用](#web界面使用)
5. [常见问题解决](#常见问题解决)
6. [安全注意事项](#安全注意事项)

---

## 🎯 基础知识

### 什么是 Freqtrade？
Freqtrade 是一个**自动交易机器人**，可以：
- 📊 自动分析市场数据
- 🤖 根据策略自动买卖加密货币
- 📈 帮您24小时监控市场
- 💰 在您睡觉时也能交易赚钱

### 您当前的配置状态
- ✅ **交易所**: Binance（币安）
- ✅ **API权限**: 只读模式（安全，无法真实交易）
- ✅ **网络**: 已配置代理，连接正常
- ✅ **资金**: 虚拟资金1000 USDT（用于测试）

---

## 🔧 启动前准备

### 第一步：打开终端
1. 按 `Command + 空格键` 打开聚焦搜索
2. 输入 `终端` 或 `Terminal`
3. 按回车键打开

### 第二步：进入项目目录
在终端中输入以下命令（复制粘贴即可）：
```bash
cd /Users/<USER>/freqtrade
```

### 第三步：设置网络代理（重要！）
每次使用前都要执行这两个命令：
```bash
export http_proxy="http://127.0.0.1:8118"
export https_proxy="http://127.0.0.1:8118"
```

### 第四步：激活虚拟环境
```bash
source .venv/bin/activate
```
✅ 成功后，终端前面会显示 `(.venv)`

---

## 🎮 四种使用模式

### 1️⃣ 干跑模式（推荐新手）
**用途**: 模拟交易，不花真钱，安全学习

**启动命令**:
```bash
python -m freqtrade trade --config user_data/config.json --dry-run
```

**看到什么**:
- 大量滚动的日志信息
- 显示"dry_run enabled"（干跑模式启用）
- 模拟买卖信号和价格

**如何停止**: 按 `Ctrl + C`

---

### 2️⃣ Web界面模式（最推荐！）
**用途**: 图形化界面，像使用网站一样简单

**启动命令**:
```bash
python -m freqtrade webserver --config user_data/config.json
```

**使用步骤**:
1. 启动后看到 "Uvicorn running on http://127.0.0.1:8080"
2. 打开浏览器（Safari、Chrome等）
3. 地址栏输入: `http://127.0.0.1:8080`
4. 登录信息:
   - 用户名: `freqtrader`
   - 密码: `SuperSecurePassword`

**界面功能**:
- 📊 实时图表
- 💰 账户余额
- 📈 交易历史
- ⚙️ 策略设置

---

### 3️⃣ 回测模式
**用途**: 用历史数据测试策略效果

**第一步 - 下载历史数据**:
```bash
python -m freqtrade download-data --config user_data/config.json --pairs BTC/USDT ETH/USDT --timeframes 5m --days 30
```

**第二步 - 运行回测**:
```bash
python -m freqtrade backtesting --config user_data/config.json --strategy SampleStrategy
```

**结果解读**:
- 会显示总收益、胜率等数据
- 正数表示盈利，负数表示亏损

---

### 4️⃣ 实盘模式（需要交易权限）
**⚠️ 警告**: 会花真钱！新手请先用干跑模式练习

**启动命令**:
```bash
python -m freqtrade trade --config user_data/config.json
```

**前提条件**:
- 必须在Binance开启API交易权限
- 账户有足够资金
- 充分理解风险

---

## 🖥️ Web界面详细使用

### 登录步骤
1. 启动Web服务器（见上面命令）
2. 浏览器打开 `http://127.0.0.1:8080`
3. 输入用户名密码登录

### 主要功能区域

#### 📊 Dashboard（仪表盘）
- **当前状态**: 显示机器人是否运行
- **总资产**: 显示账户总价值
- **今日盈亏**: 显示当天收益情况

#### 💹 Trading（交易）
- **开放订单**: 当前正在进行的交易
- **交易历史**: 过往所有交易记录
- **盈亏统计**: 详细的收益分析

#### 📈 Charts（图表）
- **价格图表**: 实时价格走势
- **技术指标**: 各种分析指标
- **买卖信号**: 策略产生的交易信号

#### ⚙️ Settings（设置）
- **策略配置**: 调整交易策略参数
- **风险管理**: 设置止损、止盈
- **通知设置**: 配置消息提醒

---

## 🛠️ 常见问题解决

### Q1: 终端显示"连接超时"
**解决方法**:
```bash
# 重新设置代理
export http_proxy="http://127.0.0.1:8118"
export https_proxy="http://127.0.0.1:8118"
```

### Q2: 网页打不开 127.0.0.1:8080
**检查步骤**:
1. 确认Web服务器已启动
2. 终端显示"Uvicorn running"
3. 检查浏览器地址是否正确

### Q3: 登录失败
**用户名**: `freqtrader`
**密码**: `SuperSecurePassword`
（注意大小写）

### Q4: 看不懂日志信息
**正常现象**: 
- INFO: 普通信息，绿色，正常
- WARNING: 警告，黄色，注意但不严重
- ERROR: 错误，红色，需要处理

### Q5: 如何停止程序
- 在终端按 `Ctrl + C`
- 或者直接关闭终端窗口

---

## 🔒 安全注意事项

### ✅ 当前安全状态
- API只有读取权限，无法真实交易
- 使用虚拟资金测试
- 所有操作都是模拟的

### ⚠️ 如果要实盘交易
1. **小额测试**: 先用少量资金测试
2. **设置止损**: 限制最大亏损
3. **定期检查**: 每天查看交易情况
4. **备份配置**: 保存重要设置文件

### 🚫 绝对不要
- 把API密钥告诉别人
- 在不理解的情况下修改配置
- 投入超过承受能力的资金
- 让机器人无人监管运行

---

## 🎯 新手建议使用顺序

### 第1周：熟悉界面
1. 使用Web界面模式
2. 观察各种数据和图表
3. 了解基本概念

### 第2周：测试策略
1. 运行干跑模式
2. 观察模拟交易
3. 学习交易日志

### 第3周：历史回测
1. 下载历史数据
2. 测试不同策略
3. 分析回测结果

### 第4周及以后：
1. 深入学习策略编写
2. 优化参数设置
3. 考虑是否实盘（谨慎！）

---

## 📞 获取帮助

如果遇到问题：
1. 先查看本说明文档
2. 检查终端错误信息
3. 重启程序试试
4. 记录具体错误信息寻求帮助

**记住**: 学习需要时间，不要急于求成！先用模拟模式充分练习。
